<?php

  namespace classes;
  trait TimesTrait {

    private function duration(string $start, string $end, int $offsetMinutes = 0): string {
      $start = new \DateTime($start . " + $offsetMinutes minutes");
      $end = new \DateTime($end);
      $interval = $start->diff($end);
      return ($start < $end) ? $interval->format('%H:%I:%S') : '00:00:00';
    }

    function addTimes(array $times): string {
      $totalSeconds = 0;

      foreach ($times as $time) {
        $isNegative = str_starts_with($time, '-');
        $time = ltrim($time, '-');

        [$hours, $minutes, $seconds] = array_map('intval', explode(':', $time));

        $timeInSeconds = ($hours * 3600) + ($minutes * 60) + $seconds;
        if ($isNegative) $timeInSeconds = -$timeInSeconds;

        $totalSeconds += $timeInSeconds;
      }
      $sign = $totalSeconds < 0 ? '-' : '';
      $totalSeconds = abs($totalSeconds);
      $hours = floor($totalSeconds / 3600);
      $minutes = floor($totalSeconds / 60) % 60;
      $seconds = $totalSeconds % 60;

      return sprintf('%s%02d:%02d:%02d', $sign, $hours, $minutes, $seconds);
    }

    function subtractTimes(string $time1, string $time2, bool $allowNegative = true): string {
      [$hours1, $minutes1, $seconds1] = array_map('intval', explode(':', $time1));
      [$hours2, $minutes2, $seconds2] = array_map('intval', explode(':', $time2));

      $totalSeconds1 = ($hours1 * 3600) + ($minutes1 * 60) + $seconds1;
      $totalSeconds2 = ($hours2 * 3600) + ($minutes2 * 60) + $seconds2;

      if ($allowNegative) {
        $resultSeconds = $totalSeconds1 - $totalSeconds2;
        $negative = $resultSeconds < 0;
        $resultSeconds = abs($resultSeconds);
      }
      else {
        $resultSeconds = max(0, $totalSeconds1 - $totalSeconds2);
        $negative = false;
      }

      $hours = floor($resultSeconds / 3600);
      $minutes = floor(($resultSeconds % 3600) / 60);
      $seconds = $resultSeconds % 60;

      return ($negative ? '-' : '') . sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
    }

    function formatHoursAsString(float $hours): string {
      $hours = round($hours, 2);
      $formattedHours = floor($hours);
      $formattedMinutes = round(($hours - $formattedHours) * 60);
      return sprintf('%02d:%02d:00', $formattedHours, $formattedMinutes);
    }
  }