<script setup>
import {ref, watch, defineProps} from "vue";
import {projectStatus} from "@/helpers/constants.js";
import {eventHasDeclines, eventHasInternalConflicts, eventHasExternalConflicts} from "@/helpers/helpers.js";

const props = defineProps({
  projects: {
    type: Array,
    required: true,
  },
  draggedEventEmployee: {
    type: Object,
    required: true,
  },
  sourceCalendarEvent: {
    type: Object,
    required: true,
  },
  onDragStart: {
    type: Function,
    required: true,
  },
  onDragEnd: {
    type: Function,
    required: true,
  },
  onDragEnter: {
    type: Function,
    required: true,
  },
  onDragLeave: {
    type: Function,
    required: true,
  },
  onDrop: {
    type: Function,
    required: true,
  },
  filterEvents: {
    type: Function,
    required: true,
  },
  removeEmployeeFromEvent: {
    type: Function,
    required: true,
  },
  showEmployeeDetails: {
    type: Function,
    required: true,
  },
  employees: {
    type: Array,
    required: true,
  },
});

const expandedPanels = ref([]);

watch(
  () => props.projects,
  (newProjects) => {
    expandedPanels.value = newProjects
      .map((project, index) => (props.filterEvents(project.events).some(calendarEvent => calendarEvent.employees.length) ? index : null))
      .filter((index) => index !== null);
  },
  {immediate: true, deep: true}
);

const isDropZone = (calendarEvent) => {
  return Object.keys(props.draggedEventEmployee).length && calendarEvent.id !== props.sourceCalendarEvent.id;
}

const getEmployee = (eventEmployee) => {
  return props.employees.find(emp => emp.id === eventEmployee.employee.id);
}
const handleEmployeeClick = (eventEmployee) => {
  const employee = getEmployee(eventEmployee);
  if (employee) {
    props.showEmployeeDetails(employee);
  }
}
const getEmployeeAvailability = (eventEmployee) => {
  const employee = getEmployee(eventEmployee);
  return employee?._availability;
}
</script>

<template>
  <v-expansion-panels
    v-model="expandedPanels"
    multiple
    variant="accordion"
  >
    <template
      v-for="project in projects"
      :key="project.id"
    >
      <v-expansion-panel
        v-for="calendarEvent in filterEvents(project.events, true)"
        :key="calendarEvent.id"
        :disabled="!calendarEvent.employees.length"
        :class="{
          dropzone: isDropZone(calendarEvent),
          details: true,
          'mb-3': true,
        }"
        :color="(calendarEvent.employees.length) ? projectStatus.find(status => status.value === project.status)?.color : 'transparent'"
        elevation="0"
        @drop="(event) => onDrop(calendarEvent, event)"
        @dragenter="(event) => onDragEnter(calendarEvent, event)"
        @dragover.prevent
        @dragleave="(event) => onDragLeave(event)"
      >
        <v-expansion-panel-title
          :class="{
            'expansion-project-title pa-2': true,
          }"
        >
          <v-icon
            v-if="eventHasInternalConflicts(calendarEvent)"
            class="mr-1"
            size="20"
            title="Dit project heeft medewerkers die dubbel zijn ingepland"
          >
            mdi-alert-circle-outline
          </v-icon>
          <v-icon
            v-if="eventHasExternalConflicts(calendarEvent)"
            class="mr-1"
            size="20"
            title="Dit project heeft medewerkers die niet beschikbaar zijn op dit moment"
          >
            mdi-alert-circle-outline
          </v-icon>
          <v-icon
            v-if="eventHasDeclines(calendarEvent)"
            class="mr-1"
            size="20"
            title="Dit project heeft medewerkers die de afspraak hebben geweigerd"
          >
            mdi-close-circle
          </v-icon>
          {{ project.name }}
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <v-list
            density="compact"
            class="pa-0"
          >
            <v-card
              v-for="(eventEmployee, eventEmployeeIndex) in calendarEvent.employees"
              :key="eventEmployee.id"
              draggable="true"
              class="draggable mt-3"
              :class="{
                'internal-conflict': eventEmployee.conflict?.internal,
                'external-conflict': eventEmployee.conflict && !eventEmployee.conflict?.internal,
                'declined': eventEmployee.responseStatus === 'declined',
              }"
              border
              :ripple="false"
              @dragstart="(event) => onDragStart(eventEmployee, calendarEvent, event)"
              @dragend="onDragEnd"
              @click="handleEmployeeClick(eventEmployee)"
            >
              <v-list-item-title class="employee-wrapper pa-2">
                <span>
                  <v-icon
                    v-if="eventEmployee.conflict?.internal"
                    class="mr-1"
                    size="20"
                    color="indufastPurple"
                    title="Deze medewerker is dubbel ingepland"
                  >
                    mdi-alert-circle-outline
                  </v-icon>
                  <v-icon
                    v-else-if="eventEmployee.conflict && !eventEmployee.conflict?.internal"
                    class="mr-1"
                    size="20"
                    color="indufastRed"
                    title="Deze medewerker is niet meer beschikbaar op dit moment"
                  >
                    mdi-alert-circle-outline
                  </v-icon>
                  <v-icon
                    v-if="eventEmployee.responseStatus === 'declined'"
                    class="mr-1"
                    size="20"
                    color="error"
                    title="Deze medewerker heeft deze afspraak geweigerd"
                  >
                    mdi-close-circle
                  </v-icon>
                  <v-icon
                    v-if="eventEmployee.responseStatus === 'tentative'"
                    class="mr-1"
                    size="20"
                    color="indufastBlue"
                    title="Deze medewerker heeft aangegeven misschien aanwezig te zijn"
                  >
                    mdi-clock-outline
                  </v-icon>
                  <v-icon
                    v-if="getEmployeeAvailability(eventEmployee) === 'not_available_non_working_day'"
                    icon="mdi-briefcase-variant-off-outline"
                    class="mr-1"
                    size="20"
                    color="indufastPurple"
                    title="Deze medewerker is roostervrij op deze datum"
                  />
                  {{ eventEmployee.employee.name }}
                </span>
                <span>
                  <v-icon
                    v-if="!eventEmployee.employee.active"
                    color="indufastRed"
                    title="Inactieve gebruiker"
                  >
                    mdi-cancel
                  </v-icon>
                  <v-icon
                    v-if="calendarEvent.team_lead_employee_id === eventEmployee.employee.id"
                    color="secondary"
                    title="Contactpersoon bouwplaats"
                    icon="mdi-account-hard-hat"
                  />
                  <v-icon
                    v-if="calendarEvent.material_load_employee_ids?.includes(eventEmployee.employee.id)"
                    color="secondary"
                    title="Lader"
                    icon="mdi-forklift"
                  />
                  {{ eventEmployee.employee.rank }}{{ eventEmployee.employee.rank_number }}
                  <v-icon
                    color="indufastRed"
                    @click.prevent.stop="removeEmployeeFromEvent(calendarEvent, eventEmployeeIndex)"
                  >
                    mdi-delete
                  </v-icon>
                </span>
              </v-list-item-title>
            </v-card>
          </v-list>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </template>
  </v-expansion-panels>
</template>

<style scoped>
.dropzone * {
  pointer-events: none;
}
</style>

<style>
.expansion-project-title {
  min-height: 3em !important;
  text-transform: uppercase;
  font-weight: bold;
}
.internal-conflict {
  border: 1.5px solid purple !important;
}
.external-conflict, .declined {
  border: 1.5px solid red !important;
}
</style>
