<script setup>
import { ref } from "vue";
import { formatDate } from "date-fns";
import { useRoute, useRouter } from 'vue-router';
import { eventType, industryTypes, projectStatus } from "@/helpers/constants.js";
import { eventHasInternalConflicts, eventHasExternalConflicts, projectHasFTEError } from "@/helpers/helpers.js";

const route = useRoute();
const router = useRouter();
const eventChanges = ref(new Map()); // Track changes per event
const hasUnsavedProjectChanges = ref(false);

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true
  },
  project: {
    type: Object,
    default: () => ({})
  },
  projectIsChanged: {
    type: Function,
    default: () => false
  },
  hasUnsavedChanges: {
    type: Boolean,
    default: false
  },
  markCalendarEventAsChanged: {
    type: Function,
    default: () => {}
  },
  loading: {
    type: <PERSON><PERSON><PERSON>,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'edit', 'save']);

const selectTeamLead = (employee, calendarEvent) => {
  if (props.readonly) return;

  if (!eventChanges.value.has(calendarEvent.id)) {
    eventChanges.value.set(calendarEvent.id, {
      team_lead_employee_id: calendarEvent.team_lead_employee_id,
      team_lead: calendarEvent.team_lead,
      material_load_employee_ids: [...(calendarEvent.material_load_employee_ids || [])],
      material_loaders: [...(calendarEvent.material_loaders || [])]
    });
  }

  const changes = eventChanges.value.get(calendarEvent.id);
  // Toggle team lead
  if (changes.team_lead_employee_id === employee.id) {
    changes.team_lead_employee_id = null;
    changes.team_lead = null;
  } else {
    changes.team_lead_employee_id = employee.id;
    changes.team_lead = employee;
  }
  hasUnsavedProjectChanges.value = true;
};

const selectMaterialLoader = (employee, calendarEvent) => {
  if (props.readonly) return;

  if (!eventChanges.value.has(calendarEvent.id)) {
    eventChanges.value.set(calendarEvent.id, {
      team_lead_employee_id: calendarEvent.team_lead_employee_id,
      team_lead: calendarEvent.team_lead,
      material_load_employee_ids: calendarEvent.material_load_employee_ids ? [...calendarEvent.material_load_employee_ids] : [],
      material_loaders: calendarEvent.material_loaders ? [...calendarEvent.material_loaders] : []
    });
  }

  const changes = eventChanges.value.get(calendarEvent.id);
  const index = changes.material_load_employee_ids.indexOf(employee.id);
  if (index === -1) {
    changes.material_load_employee_ids.push(employee.id);
    changes.material_loaders.push(employee);
  } else {
    changes.material_load_employee_ids.splice(index, 1);
    changes.material_loaders.splice(index, 1);
  }
  hasUnsavedProjectChanges.value = true;
};

const editProject = async () => {
  emit('edit', props.project);
};

const isCurrentSelectedDate = (event) => {
  const date = new Date(event.start);
  return (formatDate(date, "yyyy-MM-dd") === route.params.date)
}

const openPlanningPage = (event) => {
  if (!isCurrentSelectedDate(event) || route.name !== '/planning') {
    const date = new Date(event.start);
    router.push({
      name: '/planning', params: {
        date: formatDate(date, "yyyy-MM-dd"),
      }
    });
  }
  emit('update:modelValue', false);
}

const getFte = (project, event) => {
  return event.fte || project.fte;
}

const saveChanges = () => {
  // Find matching events in the actual project and apply changes
  props.project.events.forEach(event => {
    if (eventChanges.value.has(event.id)) {
      const changes = eventChanges.value.get(event.id);

      // Apply changes directly to the event
      event.team_lead_employee_id = changes.team_lead_employee_id;
      event.team_lead = changes.team_lead;
      event.material_load_employee_ids = changes.material_load_employee_ids;
      event.material_loaders = changes.material_loaders;

      props.markCalendarEventAsChanged(event);
    }
  });

  hasUnsavedProjectChanges.value = false;
  eventChanges.value.clear();
  emit('save');
};

const closeDialog = () => {
  hasUnsavedProjectChanges.value = false;
  emit('update:modelValue', false);
};

const getEffectiveTeamLeadId = (event) => {
  if (eventChanges.value.has(event.id)) {
    return eventChanges.value.get(event.id).team_lead_employee_id;
  }
  return event.team_lead_employee_id;
};

const getEffectiveMaterialLoaderIds = (event) => {
  if (eventChanges.value.has(event.id)) {
    return eventChanges.value.get(event.id).material_load_employee_ids;
  }
  return event.material_load_employee_ids;
};
</script>

<template>
  <v-dialog
    :model-value="modelValue"
    width="900px"
    scrollable
    @update:model-value="closeDialog"
  >
    <v-card>
      <v-toolbar
        :color="projectStatus.find(status => status.value === project.status)?.color"
      >
        <v-toolbar-title>
          <v-icon
            v-if="!project.is_plannable"
            class="mr-1"
            size="small"
            title="Dit project heeft planningsconflicten, controleer of medewerkers dubbel zijn ingepland"
          >
            mdi-alert-circle-outline
          </v-icon>
          {{ project.name }}
        </v-toolbar-title>
        <v-toolbar-items>
          <v-btn
            :icon="!readonly ? 'mdi-pencil' : 'mdi-eye'"
            :title="hasUnsavedChanges || hasUnsavedProjectChanges ? 'Je hebt niet-opgeslagen wijzigingen' : 'Bewerk project'"
            :disabled="hasUnsavedChanges || hasUnsavedProjectChanges || loading"
            @click="editProject"
          />
          <v-btn
            icon="mdi-close"
            @click="closeDialog"
          />
        </v-toolbar-items>
      </v-toolbar>
      <v-card-text class="pa-2">
        <v-card-title>
          Projectinformatie
        </v-card-title>
        <v-list
          density="compact"
          class="pt-0"
        >
          <v-list-item
            v-if="project.events.length > 1"
            prepend-icon="mdi-calendar"
            slim
          >
            {{ $filters.ucFirst($filters.formatDate(project.start)) }} t/m {{ $filters.formatDate(project.end) }}
          </v-list-item>
          <v-list-item
            v-if="project.events.length === 1"
            prepend-icon="mdi-calendar"
            slim
          >
            {{ $filters.ucFirst($filters.formatDate(project.start)) }}
          </v-list-item>
          <v-list-item
            prepend-icon="mdi-map-marker"
            slim
          >
            <a
              :href="'https://maps.google.com/?q=' + encodeURIComponent(project.address)"
              target="_blank"
            >
              {{ project.address }}
            </a>
          </v-list-item>
          <v-list-item
            v-if="getFte(project, project.events[0])"
            prepend-icon="mdi-account-group"
            :title="getFte(project, project.events[0]) + ' FTE'"
            slim
          />
          <v-list-item
            v-if="project.remark"
            prepend-icon="mdi-note"
            slim
          >
            <p
              class="markup"
              v-html="project.remark"
            />
          </v-list-item>
          <v-list-item
            prepend-icon="mdi-pound-box-outline"
            slim
          >
            {{ project.project_number }} / {{ project.project_number_exact }} (Exact)
          </v-list-item>
        </v-list>
        <template v-if="project.contact_name || project.contact_number || project.contact_email">
          <v-divider />
          <v-card-title class="pb-0">
            Contactinformatie klant
          </v-card-title>
          <v-list
            density="compact"
            class="pt-0"
          >
            <v-list-item
              v-if="project.contact_name"
              prepend-icon="mdi-account"
              slim
            >
              {{ project.contact_name }}
            </v-list-item>
            <v-list-item
              v-if="project.contact_number"
              prepend-icon="mdi-phone"
              slim
            >
              <a :href="'tel:' + project.contact_number">{{ project.contact_number }}</a>
            </v-list-item>
            <v-list-item
              v-if="project.contact_email"
              prepend-icon="mdi-email"
              slim
            >
              <a :href="'mailto:' + project.contact_email">{{ project.contact_email }}</a>
            </v-list-item>
          </v-list>
        </template>
        <template
          v-for="(projectevent, projectIndex) in project.events"
          :key="projectIndex"
        >
          <v-divider />
          <v-card-title
            :class="{
              'text-indufastOrange': projectevent.type === 'blast',
              'text-indufastRed':
                (eventHasExternalConflicts(projectevent) || projectHasFTEError(project, projectevent)) || (!getEffectiveTeamLeadId(projectevent) && projectevent.type === 'work'),
              'text-indufastPurple': eventHasInternalConflicts(projectevent)
            }"
          >
            <v-icon
              v-if="projectevent.type === 'blast'"
              class="mr-1"
              size="small"
              :title="eventType.find(type => type.value === projectevent.type)?.title"
            >
              mdi-handshake
            </v-icon>
            <v-icon
              v-if="eventHasExternalConflicts(projectevent)"
              size="small"
              color="indufastRed"
              title="Deze datum heeft medewerkers die niet meer beschikbaar zijn op dit moment"
              icon="mdi-alert-circle-outline"
            />
            <v-icon
              v-if="eventHasInternalConflicts(projectevent)"
              size="small"
              color="indufastPurple"
              title="Deze datum heeft dubbel ingeplande medewerkers"
              icon="mdi-alert-circle-outline"
            />
            <v-icon
              v-if="projectevent.type === 'work' && projectHasFTEError(project, projectevent)"
              size="small"
              color="indufastRed"
              title="Deze datum heeft te weinig medewerkers ingepland voor het aantal FTE's"
              icon="mdi-account-plus"
            />
            <v-icon
              v-if="projectevent.type === 'work' && !getEffectiveTeamLeadId(projectevent)"
              size="small"
              color="indufastRed"
              title="Geen contactpersoon bouwplaats aangewezen"
              icon="mdi-account-hard-hat-outline"
            />
            <v-icon
              v-if="projectevent.type === 'work' && project.material_load && !getEffectiveMaterialLoaderIds(projectevent)?.length"
              size="small"
              color="indufastRed"
              title="Geen lader aangewezen terwijl er materiaal geladen moet worden"
              icon="mdi-forklift"
            />
            {{ $filters.ucFirst($filters.formatDate(projectevent.start)) }}{{ projectevent.type === 'blast' ? ": Stralen" : "" }}
          </v-card-title>
          <v-list
            density="compact"
            class="pt-0"
          >
            <v-list-item
              v-if="projectevent.type === 'blast' && !projectevent.confirmed"
              prepend-icon="mdi-close"
              class="text-indufastRed"
              title="Niet bevestigd"
              slim
            />
            <v-list-item
              prepend-icon="mdi-clock-outline"
              slim
            >
              {{ $filters.formatTime(projectevent.start) }} - {{ $filters.formatTime(projectevent.end) }}
              <v-icon
                v-if="projectevent.type === 'work'"
                icon="mdi-clipboard-account"
                color="indufastGreen"
                variant="plain"
                title="Open planning"
                @click="openPlanningPage(projectevent)"
              />
            </v-list-item>
            <v-list-item
              v-if="projectevent.remark"
              prepend-icon="mdi-note"
              :title="projectevent.remark"
              slim
            />
            <v-list-item
              v-if="projectevent.employees.length"
              prepend-icon="mdi-account-group"
              slim
            >
              <v-card
                v-for="({ employee }) in projectevent.employees"
                :key="employee.id"
                class="employee mt-2 mb-2"
                border
              >
                <v-card-text class="employee-wrapper pa-2">
                  <span class="employee-name">
                    <v-icon
                      v-if="employee._availability === 'not_available_non_working_day'"
                      icon="mdi-briefcase-variant-off-outline"
                      color="indufastPurple"
                      title="Deze medewerker is roostervrij op deze datum"
                    />
                    {{ employee.name }}
                  </span>
                  <span>
                    <v-icon
                      v-for="(industry, industryIndex) in employee.industries"
                      :key="industryIndex"
                      class="mr-1"
                      :color="industryTypes.find(type => type.value === industry)?.color"
                      :title="industryTypes.find(type => type.value === industry)?.title"
                      :icon="industryTypes.find(type => type.value === industry)?.icon"
                    />
                    {{ employee.rank }}{{ employee.rank_number }}
                  </span>
                </v-card-text>
                <v-card-actions class="pa-0 pl-2 pr-2 employee-actions">
                  <v-checkbox-btn
                    :disabled="!employee.team_lead"
                    color="indufastGreen"
                    density="compact"
                    true-icon="mdi-account-hard-hat"
                    :false-icon="employee.team_lead ? 'mdi-account-hard-hat-outline' : 'mdi-account-off-outline'"
                    :model-value="projectevent.team_lead_employee_id === employee.id"
                    :readonly="readonly"
                    @click="selectTeamLead(employee, projectevent)"
                  />
                  <v-checkbox-btn
                    color="indufastBlue"
                    density="compact"
                    true-icon="mdi-forklift"
                    false-icon="mdi-forklift"
                    :model-value="projectevent.material_load_employee_ids?.includes(employee.id)"
                    :readonly="readonly"
                    @click="selectMaterialLoader(employee, projectevent)"
                  />
                </v-card-actions>
              </v-card>
            </v-list-item>
          </v-list>
        </template>
      </v-card-text>
      <v-card-actions v-if="!readonly">
        <v-btn
          variant="elevated"
          color="primary"
          class="confirm"
          prepend-icon="mdi-content-save"
          text="Opslaan"
          :loading="loading"
          @click="saveChanges"
        />
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<style scoped>
.confirm {
  margin-right: 16px;
  margin-bottom: 16px;
}

.employee-wrapper {
  display: flex !important;
  justify-content: space-between;
  background-color: rgb(var(--v-theme-GSDBackground));
}

.employee {
  display: flex;
  width: 100%;
}

.employee-actions {
  min-height: auto;
}
</style>
