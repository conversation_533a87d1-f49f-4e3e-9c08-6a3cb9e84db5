<?php

  use classes\ApiResponse;
  use domain\google\service\GoogleMapsService;

  trait IndufastGoogleApiTrait {
    use PropertyCastTrait;

    public function executeSearchLocations(): void {
      $rules = [
        'query' => 'required|string',
      ];
      $data = $this->validateData($_GET, $rules)->getValidatedData();

      $service = new GoogleMapsService();
      $locations = $service->getLocations($data['query']);

      if (isset($locations['error_message'])) {
        ApiResponse::sendResponseError('Google Maps API error: ' . $locations['error_message']);
      } else {
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $locations['predictions'] ?? []);
      }
    }
  }